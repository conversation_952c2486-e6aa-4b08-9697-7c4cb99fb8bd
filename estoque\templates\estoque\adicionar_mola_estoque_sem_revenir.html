{% extends 'base.html' %}
{% load static %}

{% block title %}Adicionar Mola ao Estoque Sem Revenir{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus"></i> Adicionar Mola ao Estoque Sem Revenir
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'estoque-sem-revenir-list' %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Voltar
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="mola_id" class="form-label"><PERSON><PERSON></label>
                                    <select name="mola_id" id="mola_id" class="form-select" required>
                                        <option value="">Selecione uma mola...</option>
                                        {% for mola in molas_disponiveis %}
                                            <option value="{{ mola.id }}">{{ mola.codigo }} - {{ mola.cliente }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">
                                        Apenas molas que não estão no estoque sem revenir são exibidas.
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="quantidade" class="form-label">Quantidade</label>
                                    <input type="number" 
                                           name="quantidade" 
                                           id="quantidade" 
                                           class="form-control" 
                                           min="1" 
                                           required>
                                    <div class="form-text">
                                        Quantidade de unidades a adicionar ao estoque sem revenir.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="form-group mb-3">
                                    <label for="observacoes" class="form-label">Observações</label>
                                    <textarea name="observacoes" 
                                              id="observacoes" 
                                              class="form-control" 
                                              rows="3" 
                                              placeholder="Observações sobre esta adição (opcional)"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{% url 'estoque-sem-revenir-list' %}" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-times"></i> Cancelar
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Adicionar ao Estoque
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    {% if not molas_disponiveis %}
                        <div class="alert alert-warning mt-4">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Atenção:</strong> Não há molas disponíveis para adicionar ao estoque sem revenir.
                            Todas as molas já estão no estoque sem revenir ou não existem molas cadastradas.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focar no campo de mola ao carregar a página
    document.getElementById('mola_id').focus();
    
    // Quando uma mola for selecionada, focar no campo quantidade
    document.getElementById('mola_id').addEventListener('change', function() {
        if (this.value) {
            document.getElementById('quantidade').focus();
        }
    });
    
    // Permitir navegação com Enter
    document.getElementById('quantidade').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            document.getElementById('observacoes').focus();
        }
    });
});
</script>
{% endblock %}
