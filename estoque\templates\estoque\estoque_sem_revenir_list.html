{% extends 'base.html' %}
{% load static %}

{% block title %}Estoque Sem Revenir{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-boxes"></i> Estoque Sem Revenir
                    </h3>
                    <div>
                        <a href="{% url 'adicionar-mola-estoque-sem-revenir' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Adicionar <PERSON>
                        </a>
                        <a href="{% url 'relatorio-estoque-sem-revenir' %}" class="btn btn-info">
                            <i class="fas fa-chart-bar"></i> Relatório
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Estatísticas -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-cubes"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total de Molas</span>
                                    <span class="info-box-number">{{ total_molas }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-calculator"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Quantidade Total</span>
                                    <span class="info-box-number">{{ total_quantidade }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabela de Estoque -->
                    {% if estoques %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Código</th>
                                        <th>Cliente</th>
                                        <th>Quantidade</th>
                                        <th>Última Movimentação</th>
                                        <th>Observações</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for estoque in estoques %}
                                        <tr>
                                            <td>
                                                <strong>{{ estoque.mola.codigo }}</strong>
                                            </td>
                                            <td>{{ estoque.mola.cliente }}</td>
                                            <td>
                                                <span class="badge bg-primary">{{ estoque.quantidade }}</span>
                                            </td>
                                            <td>{{ estoque.data_ultima_movimentacao|date:"d/m/Y H:i" }}</td>
                                            <td>
                                                {% if estoque.observacoes %}
                                                    {{ estoque.observacoes }}
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{% url 'movimentar-estoque-sem-revenir' estoque.id %}" 
                                                   class="btn btn-sm btn-warning" 
                                                   title="Movimentar">
                                                    <i class="fas fa-exchange-alt"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Paginação -->
                        {% if is_paginated %}
                            <nav aria-label="Navegação de páginas">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1">Primeira</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Anterior</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            Página {{ page_obj.number }} de {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Próxima</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Última</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            Nenhuma mola encontrada no estoque sem revenir.
                            <br>
                            <a href="{% url 'adicionar-mola-estoque-sem-revenir' %}" class="btn btn-primary mt-2">
                                <i class="fas fa-plus"></i> Adicionar Primeira Mola
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
